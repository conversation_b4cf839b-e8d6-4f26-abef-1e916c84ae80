// Custom hooks for data fetching with React Query
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"
import { api } from "@/lib/api"
import type { User, Friend, FriendRequest, Bill, DashboardStats, Analytics } from "@/lib/types"
import { useToast } from "@/hooks/use-toast"

import { QUERY_KEYS, CACHE_TIMES } from "@/lib/constants"

// Query keys for cache management
export const queryKeys = QUERY_KEYS

// User hooks
export function useCurrentUser() {
  return useQuery({
    queryKey: queryKeys.CURRENT_USER,
    queryFn: api.getCurrentUser,
    staleTime: CACHE_TIMES.MEDIUM,
  })
}

export function useSearchUsers(query: string, enabled = false) {
  return useQuery({
    queryKey: queryKeys.SEARCH_USERS(query),
    queryFn: () => api.searchUsers(query),
    enabled: enabled && query.trim().length > 0,
    staleTime: CACHE_TIMES.VERY_SHORT,
  })
}

export function useSearchFriends(query: string, enabled = false) {
  return useQuery({
    queryKey: queryKeys.SEARCH_FRIENDS(query),
    queryFn: () => api.searchFriends(query),
    enabled: enabled && query.trim().length > 0,
    staleTime: CACHE_TIMES.VERY_SHORT,
  })
}

// Friends hooks
export function useFriends() {
  return useQuery({
    queryKey: queryKeys.FRIENDS,
    queryFn: api.getFriends,
    staleTime: CACHE_TIMES.SHORT,
  })
}

export function useSendFriendRequest() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: api.sendFriendRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.FRIENDS })
      toast({
        title: "Успешно",
        description: "Запрос на добавление в друзья отправлен",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось отправить запрос",
        variant: "destructive",
      })
    },
  })
}

export function useRespondToFriendRequest() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: ({ requestId, action }: { requestId: string; action: "accept" | "reject" }) =>
      api.respondToFriendRequest(requestId, action),
    onSuccess: (_, { action }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.FRIENDS })
      toast({
        title: "Успешно",
        description: action === "accept" ? "Запрос принят" : "Запрос отклонен",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось обработать запрос",
        variant: "destructive",
      })
    },
  })
}

// Bills hooks
export function useBills() {
  return useQuery({
    queryKey: queryKeys.BILLS,
    queryFn: api.getBills,
    staleTime: CACHE_TIMES.SHORT,
  })
}

export function useBill(id: string) {
  return useQuery({
    queryKey: queryKeys.BILL(id),
    queryFn: () => api.getBill(id),
    enabled: !!id,
    staleTime: CACHE_TIMES.SHORT,
  })
}

export function useCreateBill() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: api.createBill,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.BILLS })
      queryClient.invalidateQueries({ queryKey: queryKeys.DASHBOARD_STATS })
      toast({
        title: "Успешно",
        description: "Счет создан",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось создать счет",
        variant: "destructive",
      })
    },
  })
}

export function useUpdateBill() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Bill> }) =>
      api.updateBill(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.BILLS })
      queryClient.invalidateQueries({ queryKey: queryKeys.BILL(id) })
      queryClient.invalidateQueries({ queryKey: queryKeys.DASHBOARD_STATS })
      toast({
        title: "Успешно",
        description: "Счет обновлен",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось обновить счет",
        variant: "destructive",
      })
    },
  })
}

export function useDeleteBill() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: api.deleteBill,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.BILLS })
      queryClient.invalidateQueries({ queryKey: queryKeys.DASHBOARD_STATS })
      toast({
        title: "Успешно",
        description: "Счет удален",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось удалить счет",
        variant: "destructive",
      })
    },
  })
}

export function useUpdateBillPayment() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: ({ billId, participantId, status }: { billId: string; participantId: string; status: string }) =>
      api.updateBillPayment(billId, participantId, status),
    onSuccess: (_, { billId }) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.BILLS })
      queryClient.invalidateQueries({ queryKey: queryKeys.BILL(billId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.DASHBOARD_STATS })
      toast({
        title: "Платеж подтвержден",
        description: "Статус платежа обновлен",
      })
    },
    onError: () => {
      toast({
        title: "Ошибка",
        description: "Не удалось обновить статус",
        variant: "destructive",
      })
    },
  })
}

// Dashboard hooks
export function useDashboardStats() {
  return useQuery({
    queryKey: queryKeys.DASHBOARD_STATS,
    queryFn: api.getDashboardStats,
    staleTime: CACHE_TIMES.SHORT,
  })
}

// Analytics hooks
export function useAnalytics(period: string) {
  return useQuery({
    queryKey: queryKeys.ANALYTICS(period),
    queryFn: () => api.getAnalytics(period),
    enabled: !!period,
    staleTime: CACHE_TIMES.MEDIUM,
  })
}
