// API layer for clean separation of concerns
import {
  <PERSON><PERSON>,
  Friend,
  FriendRequest,
  Bill,
  DashboardStats,
  Analytics,
  CreateBillForm, BillDetails
} from "./types"

// API functions
export const api = {
  // User APIs
  async getCurrentUser(): Promise<User> {
    const response = await fetch("/api/user/me")
    if (!response.ok) {
      throw new Error("Failed to fetch current user")
    }
    const data = await response.json()
    return data.user
  },

  async searchUsers(query: string): Promise<User[]> {
    const response = await fetch(`/api/users/search?q=${encodeURIComponent(query)}`)
    if (!response.ok) {
      throw new Error("Failed to search users")
    }
    const data = await response.json()
    return data.users || []
  },

  // Friends APIs
  async getFriends(): Promise<{
    friends: Friend[]
    sentRequests: FriendRequest[]
    receivedRequests: FriendRequest[]
  }> {
    const response = await fetch("/api/friends")
    if (!response.ok) {
      throw new Error("Failed to fetch friends")
    }
    const data = await response.json()
    return {
      friends: data.friends || [],
      sentRequests: data.sentRequests || [],
      receivedRequests: data.receivedRequests || []
    }
  },

  async sendFriendRequest(userId: string): Promise<void> {
    const response = await fetch("/api/friends/request", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({ userId })
    })
    if (!response.ok) {
      throw new Error("Failed to send friend request")
    }
  },

  async respondToFriendRequest(requestId: string, action: "accept" | "reject"): Promise<void> {
    const response = await fetch(`/api/friends/request/${requestId}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({ action })
    })
    if (!response.ok) {
      throw new Error("Failed to respond to friend request")
    }
  },

  // Bills APIs
  async getBills(): Promise<Bill[]> {
    const response = await fetch("/api/bills")
    if (!response.ok) {
      throw new Error("Failed to fetch bills")
    }
    const data = await response.json()
    return data.bills || []
  },

  async getBill(id: string): Promise<BillDetails> {
    const response = await fetch(`/api/bills/${id}`)
    if (!response.ok) {
      throw new Error("Failed to fetch bill")
    }
    const data = await response.json()
    return data.bill
  },

  async createBill(billData: CreateBillForm): Promise<Bill> {
    const response = await fetch("/api/bills", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(billData)
    })
    if (!response.ok) {
      throw new Error("Failed to create bill")
    }
    const data = await response.json()
    return data.bill
  },

  async updateBill(id: string, billData: Partial<Bill>): Promise<Bill> {
    const response = await fetch(`/api/bills/${id}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(billData)
    })
    if (!response.ok) {
      throw new Error("Failed to update bill")
    }
    const data = await response.json()
    return data.bill
  },

  async deleteBill(id: string): Promise<void> {
    const response = await fetch(`/api/bills/${id}`, {
      method: "DELETE"
    })
    if (!response.ok) {
      throw new Error("Failed to delete bill")
    }
  },

  async updateBillPayment(billId: string, participantId: string, status: string): Promise<Bill> {
    const response = await fetch(`/api/bills/${billId}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({ participantId, status })
    })
    if (!response.ok) {
      throw new Error("Failed to update bill payment")
    }
    const data = await response.json()
    return data.bill
  },

  // Dashboard APIs
  async getDashboardStats(): Promise<DashboardStats> {
    const response = await fetch("/api/dashboard/stats")
    if (!response.ok) {
      throw new Error("Failed to fetch dashboard stats")
    }
    const data = await response.json()
    return data.stats
  },

  // Analytics APIs
  async getAnalytics(period: string): Promise<Analytics> {
    const response = await fetch(`/api/analytics?period=${period}`)
    if (!response.ok) {
      throw new Error("Failed to fetch analytics")
    }
    const data = await response.json()
    return data.analytics
  }
}
