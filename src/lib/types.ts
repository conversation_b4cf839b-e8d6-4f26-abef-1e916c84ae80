// Единый источник истины для всех типов приложения
import type {
  FriendRequestStatus,
  SortField,
  SortOrder,
  FilterStatus,
  AnalyticsPeriod
} from "./constants"

// ===== БАЗОВЫЕ ТИПЫ =====

export interface User {
  _id: string
  name: string
  email: string
  image?: string
  bio?: string
}

export interface Friend {
  _id: string
  name: string
  email: string
}

export interface FriendRequest {
  _id: string
  from: { _id: string; name: string; email: string }
  to: { _id: string; name: string; email: string }
  status: FriendRequestStatus
  createdAt: string
  updatedAt: string
}

export interface BillParticipant {
  _id: string
  user: {
    _id: string
    name: string
    email: string
  }
  amountOwed: number
  status: "paid" | "unpaid"
}

export interface BillItemConsumer {
  _id: string
  user: string
  portions: number
}

export interface BillItem {
  _id: string
  name: string
  price: number
  consumers: BillItemConsumer[]
}

export interface Bill {
  _id: string
  title: string
  description?: string
  total: number
  creator: {
    _id: string
    name: string
    email: string
  }
  participants: BillParticipant[]
  items: BillItem[]
  createdAt: string
  __v?: number
}

export interface BillDetails {
  _id: string;
  creator: {
    _id: string;
    name: string;
    email: string;
  };
  title: string;
  total: number;
  items: {
    name: string;
    price: number;
    consumers: {
      user: {
        _id: string;
        name: string;
        email: string;
      };
      portions: number;
      _id: string;
    }[];
    _id: string;
  }[];
  participants: {
    user: {
      _id: string;
      name: string;
      email: string;
    };
    amountOwed: number;
    status: string;
    _id: string;
  }[];
  createdAt: string;
  __v: number;
}

export interface DashboardStats {
  totalBills: number
  totalAmount: number
  totalOwed: number
  totalOwing: number
}

export type Analytics = {
  period: string;
  totalSpent: number;
  totalOwed: number;
  totalOwing: number;
  billsCreated: number;
  billsParticipated: number;
  avgDailySpending: number;
  spendingChange: number;
  dailySpending: {
    date: string;
    amount: number;
  }[];
  topFriends: {
    name: string;
    amount: number;
  }[];
  categoryData: {
    category: string;
    amount: number;
  }[];
  totalBills: number;
}

// ===== ФИЛЬТРАЦИЯ И СОРТИРОВКА =====
// Типы экспортируются из constants.ts

// ===== API ОТВЕТЫ =====

export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  hasMore: boolean
}

// ===== ФОРМЫ =====

export interface CreateBillForm {
  title: string
  description?: string
  items: Array<{
    name: string
    price: number
    consumers: Array<{
      user: string
      portions: number
    }>
  }>
  selectedFriends: string[]
}

export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  name: string
  email: string
  password: string
  confirmPassword: string
}

export interface SearchUsersForm {
  query: string
}

// ===== СОСТОЯНИЯ UI =====

export interface LoadingState {
  isLoading: boolean
  error?: string
}

export interface FilterState {
  searchQuery: string
  sortField: SortField
  sortOrder: SortOrder
  filterStatus: FilterStatus
}

// ===== НАВИГАЦИЯ =====

export interface NavItem {
  href: string
  label: string
  icon?: React.ComponentType<{ className?: string }>
}

// ===== УВЕДОМЛЕНИЯ =====

export interface ToastMessage {
  title: string
  description?: string
  variant?: "default" | "destructive"
}

// ===== АНАЛИТИКА =====
// Типы экспортируются из constants.ts

export interface AnalyticsFilters {
  period: AnalyticsPeriod
  startDate?: string
  endDate?: string
}

// ===== УТИЛИТЫ =====

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

// ===== ЭКСПОРТ ТИПОВ ИЗ КОНСТАНТ =====
export type {
  FriendRequestStatus,
  PaymentStatus,
  SortField,
  SortOrder,
  FilterStatus,
  AnalyticsPeriod
} from "./constants"
