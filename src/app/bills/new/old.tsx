"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Navbar } from "@/components/layout/navbar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { useToast } from "@/hooks/use-toast"
import { useFriends, useCurrentUser, useCreateBill } from "@/hooks/use-api"
import { Plus, Trash2, Users, Calculator } from "lucide-react"

interface BillItem {
    id: string
    name: string
    price: number
    consumers: { userId: string, portions: number }[]
}

export default function NewBillPage() {
    const [title, setTitle] = useState("")
    const [items, setItems] = useState<BillItem[]>([
        { id: "1", name: "", price: 0, consumers: [] }
    ])
    const [selectedFriends, setSelectedFriends] = useState<string[]>([])
    const router = useRouter()
    const { toast } = useToast()

    // React Query hooks
    const { data: friendsData, isLoading: loadingFriends } = useFriends()
    const { data: currentUser, isLoading: loadingUser } = useCurrentUser()
    const createBillMutation = useCreateBill()

    const friends = friendsData?.friends || []

    // Автоматически добавляем текущего пользователя в список выбранных
    useEffect(() => {
        if (currentUser?._id && selectedFriends.length === 0) {
            setSelectedFriends([currentUser._id])
        }
    }, [currentUser, selectedFriends.length])

    const addItem = () => {
        const newItem: BillItem = {
            id: Date.now().toString(),
            name: "",
            price: 0,
            consumers: []
        }
        setItems([...items, newItem])
    }

    const removeItem = (id: string) => {
        if (items.length > 1) {
            setItems(items.filter(item => item.id !== id))
        }
    }

    const updateItem = (id: string, field: keyof BillItem, value: any) => {
        setItems(items.map(item =>
            item.id === id ? { ...item, [field]: value } : item
        ))
    }

    const toggleFriendSelection = (friendId: string) => {
        setSelectedFriends(prev =>
            prev.includes(friendId)
                ? prev.filter(id => id !== friendId)
                : [...prev, friendId]
        )
    }

    // Обновим функцию toggleItemConsumer, чтобы она работала и для текущего пользователя
    const toggleItemConsumer = (itemId: string, friendId: string) => {
        const item = items.find(item => item.id === itemId);
        if (!item) return;

        const existingConsumer = item.consumers.find(c => c.userId === friendId);

        updateItem(itemId, 'consumers',
            existingConsumer
                ? item.consumers.filter(c => c.userId !== friendId)
                : [...item.consumers, { userId: friendId, portions: 1 }]
        )
    }

    const updatePortions = (itemId: string, friendId: string, portions: number) => {
        if (portions < 1) portions = 1;

        setItems(items.map(item => {
            if (item.id === itemId) {
                return {
                    ...item,
                    consumers: item.consumers.map(consumer =>
                        consumer.userId === friendId
                            ? { ...consumer, portions }
                            : consumer
                    )
                };
            }
            return item;
        }));
    }

    // Обновим функцию calculateTotal для учета порций
    const calculateTotal = () => {
        return items.reduce((sum, item) => {
            // Общее количество порций для этого блюда
            const totalPortions = item.consumers.reduce((total, c) => total + c.portions, 0);
            // Общая стоимость = цена за порцию × количество порций
            return sum + (item.price * totalPortions);
        }, 0);
    }

    // Обновим функцию calculatePersonAmount
    const calculatePersonAmount = (friendId: string) => {
        return items.reduce((sum, item) => {
            const consumer = item.consumers.find(c => c.userId === friendId);
            if (consumer) {
                // Стоимость для этого человека = цена за порцию × количество его порций
                return sum + (item.price * consumer.portions);
            }
            return sum;
        }, 0);
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!title.trim()) {
            toast({
                title: "Ошибка",
                description: "Введите название счета",
                variant: "destructive"
            });
            return;
        }

        if (selectedFriends.length === 0) {
            toast({
                title: "Ошибка",
                description: "Выберите хотя бы одного друга",
                variant: "destructive"
            });
            return;
        }

        if (items.some(item => !item.name.trim() || item.price <= 0)) {
            toast({
                title: "Ошибка",
                description: "Заполните все позиции счета",
                variant: "destructive"
            });
            return;
        }

        // Подготовим данные для отправки
        const requestData = {
            title,
            items: items.map(item => ({
                name: item.name,
                price: item.price,
                consumers: item.consumers.map(c => ({
                    user: c.userId,
                    portions: c.portions
                }))
            })),
            selectedFriends
        };

        console.log("Sending data:", JSON.stringify(requestData, null, 2));

        createBillMutation.mutate(requestData, {
            onSuccess: () => {
                router.push("/dashboard");
            }
        });
    }

    if (loadingFriends || loadingUser) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
                <Navbar />
                <div className="container mx-auto px-4 py-8">
                    <LoadingSpinner
                        message="Загрузка данных..."
                        submessage="Получаем список друзей и информацию о пользователе"
                    />
                </div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
            <Navbar />

            <div className="container mx-auto px-4 py-8">
                {/* Header */}
                <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-10 gap-4">
                    <div>
                        <h1 className="text-4xl font-bold text-gray-900 mb-2">Создать новый счет</h1>
                        <p className="text-lg text-gray-600">
                            Добавьте позиции и выберите друзей для разделения счета
                        </p>
                    </div>
                </div>

                <form onSubmit={handleSubmit} className="space-y-8">
                    {/* Название счета */}
                    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                        <CardHeader className="pb-4">
                            <CardTitle className="text-xl">Основная информация</CardTitle>
                        </CardHeader>
                        <CardContent className="pt-2">
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Название счета
                                    </label>
                                    <Input
                                        placeholder="Например: Ужин в ресторане"
                                        value={title}
                                        onChange={(e) => setTitle(e.target.value)}
                                        required
                                        className="py-4 border-0 bg-gray-50/50 focus:bg-white focus:ring-2 focus:ring-blue-500 focus:ring-offset-0 rounded-xl text-lg shadow-sm transition-all duration-200 hover:bg-gray-100/50"
                                    />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Позиции счета */}
                    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                        <CardHeader className="pb-4">
                            <CardTitle className="flex items-center text-xl">
                                <Calculator className="h-6 w-6 mr-3 text-green-600" />
                                Позиции счета
                            </CardTitle>
                            <CardDescription className="text-gray-600">
                                Добавьте все позиции и укажите, кто что заказал
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="pt-2">
                            <div className="space-y-6">
                                {items.map((item, index) => (
                                    <div key={item.id} className="rounded-xl p-6 bg-gray-50/50 border-0 shadow-sm">
                                        <div className="flex items-center justify-between mb-4">
                                            <h4 className="font-semibold text-lg text-gray-900">Позиция {index + 1}</h4>
                                            {items.length > 1 && (
                                                <Button
                                                    type="button"
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => removeItem(item.id)}
                                                    className="text-red-600 hover:text-red-700 hover:bg-red-50 rounded-xl"
                                                >
                                                    <Trash2 className="h-5 w-5" />
                                                </Button>
                                            )}
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Название
                                                </label>
                                                <Input
                                                    placeholder="Например: Пицца Маргарита"
                                                    value={item.name}
                                                    onChange={(e) => updateItem(item.id, 'name', e.target.value)}
                                                    required
                                                    className="py-3 border-0 bg-white focus:ring-2 focus:ring-blue-500 focus:ring-offset-0 rounded-xl shadow-sm transition-all duration-200 hover:shadow-md"
                                                />
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Цена за порцию (₽)
                                                </label>
                                                <Input
                                                    type="number"
                                                    placeholder="0"
                                                    value={item.price || ''}
                                                    onChange={(e) => updateItem(item.id, 'price', parseFloat(e.target.value) || 0)}
                                                    required
                                                    className="py-3 border-0 bg-white focus:ring-2 focus:ring-blue-500 focus:ring-offset-0 rounded-xl shadow-sm transition-all duration-200 hover:shadow-md"
                                                />
                                            </div>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Кто ел/пользовался:
                                            </label>
                                            <div className="space-y-2">
                                                {/* Сначала отображаем текущего пользователя */}
                                                {currentUser && selectedFriends.includes(currentUser._id) && (
                                                    <div key={currentUser._id} className="flex items-center space-x-2">
                                                        <Button
                                                            type="button"
                                                            variant={item.consumers.some(c => c.userId === currentUser._id) ? "default" : "outline"}
                                                            size="sm"
                                                            onClick={() => toggleItemConsumer(item.id, currentUser._id)}
                                                        >
                                                            {currentUser.name} (Вы)
                                                        </Button>

                                                        {item.consumers.some(c => c.userId === currentUser._id) && (
                                                            <div className="flex items-center border rounded-md">
                                                                <Button
                                                                    type="button"
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => {
                                                                        const consumer = item.consumers.find(c => c.userId === currentUser._id);
                                                                        updatePortions(item.id, currentUser._id, (consumer?.portions || 1) - 1);
                                                                    }}
                                                                    disabled={(item.consumers.find(c => c.userId === currentUser._id)?.portions || 1) <= 1}
                                                                >
                                                                    -
                                                                </Button>
                                                                <span className="px-2">
                                    {item.consumers.find(c => c.userId === currentUser._id)?.portions || 1}x
                                  </span>
                                                                <Button
                                                                    type="button"
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => {
                                                                        const consumer = item.consumers.find(c => c.userId === currentUser._id);
                                                                        updatePortions(item.id, currentUser._id, (consumer?.portions || 1) + 1);
                                                                    }}
                                                                >
                                                                    +
                                                                </Button>
                                                            </div>
                                                        )}
                                                    </div>
                                                )}

                                                {/* Затем отображаем выбранных друзей */}
                                                {selectedFriends
                                                    .filter(friendId => currentUser && friendId !== currentUser._id)
                                                    .map(friendId => {
                                                        const friend = friends.find(f => f._id === friendId);
                                                        if (!friend) return null;

                                                        const consumer = item.consumers.find(c => c.userId === friendId);
                                                        const isSelected = !!consumer;

                                                        return (
                                                            <div key={friendId} className="flex items-center space-x-2">
                                                                <Button
                                                                    type="button"
                                                                    variant={isSelected ? "default" : "outline"}
                                                                    size="sm"
                                                                    onClick={() => toggleItemConsumer(item.id, friendId)}
                                                                >
                                                                    {friend.name}
                                                                </Button>

                                                                {isSelected && (
                                                                    <div className="flex items-center border rounded-md">
                                                                        <Button
                                                                            type="button"
                                                                            variant="ghost"
                                                                            size="sm"
                                                                            onClick={() => updatePortions(item.id, friendId, (consumer?.portions || 1) - 1)}
                                                                            disabled={(consumer?.portions || 1) <= 1}
                                                                        >
                                                                            -
                                                                        </Button>
                                                                        <span className="px-2">{consumer?.portions || 1}x</span>
                                                                        <Button
                                                                            type="button"
                                                                            variant="ghost"
                                                                            size="sm"
                                                                            onClick={() => updatePortions(item.id, friendId, (consumer?.portions || 1) + 1)}
                                                                        >
                                                                            +
                                                                        </Button>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        );
                                                    })}
                                            </div>
                                        </div>
                                    </div>
                                ))}

                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={addItem}
                                    className="w-full border-0 bg-blue-50 hover:bg-blue-100 text-blue-600 py-3 rounded-xl"
                                >
                                    <Plus className="h-5 w-5 mr-2" />
                                    Добавить позицию
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Выбор друзей */}
                    <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                        <CardHeader className="pb-4">
                            <CardTitle className="flex items-center text-xl">
                                <Users className="h-6 w-6 mr-3 text-purple-600" />
                                Участники
                            </CardTitle>
                            <CardDescription className="text-gray-600">
                                Выберите друзей, которые участвуют в счете
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="pt-2">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {/* Текущий пользователь (всегда первый и выбран по умолчанию) */}
                                {currentUser && (
                                    <div className="p-4 rounded-xl bg-gradient-to-r from-blue-50 to-blue-100/50 border border-blue-100/50 shadow-sm">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-3">
                                                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                                                    {currentUser.name.charAt(0).toUpperCase()}
                                                </div>
                                                <div>
                                                    <p className="font-semibold text-gray-900">{currentUser.name} (Вы)</p>
                                                    <p className="text-sm text-gray-600">{currentUser.email}</p>
                                                </div>
                                            </div>
                                            <div className="text-blue-600 text-xl">✓</div>
                                        </div>
                                    </div>
                                )}

                                {/* Список друзей */}
                                {friends.map(friend => (
                                    <div
                                        key={friend._id}
                                        className={`p-4 rounded-xl cursor-pointer transition-all duration-200 ${
                                            selectedFriends.includes(friend._id)
                                                ? 'bg-gradient-to-r from-blue-50 to-blue-100/50 border border-blue-100/50 shadow-sm'
                                                : 'bg-gray-50/50 hover:bg-gray-100/50 border-0 shadow-sm'
                                        }`}
                                        onClick={() => toggleFriendSelection(friend._id)}
                                    >
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-3">
                                                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${
                                                    selectedFriends.includes(friend._id)
                                                        ? 'bg-blue-500 text-white'
                                                        : 'bg-gray-200 text-gray-600'
                                                }`}>
                                                    {friend.name.charAt(0).toUpperCase()}
                                                </div>
                                                <div>
                                                    <p className="font-semibold text-gray-900">{friend.name}</p>
                                                    <p className="text-sm text-gray-600">{friend.email}</p>
                                                </div>
                                            </div>
                                            {selectedFriends.includes(friend._id) && (
                                                <div className="text-blue-600 text-xl">✓</div>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Итоги */}
                    {selectedFriends.length > 0 && (
                        <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                            <CardHeader className="pb-4">
                                <CardTitle className="text-xl">Итоги</CardTitle>
                            </CardHeader>
                            <CardContent className="pt-2">
                                <div className="space-y-4">
                                    <div className="flex justify-between items-center text-xl font-bold p-4 rounded-xl bg-gradient-to-r from-green-50 to-green-100/50 border border-green-100/50">
                                        <span>Общая сумма:</span>
                                        <span className="text-green-700">{calculateTotal().toFixed(2)} ₽</span>
                                    </div>

                                    <div className="pt-2">
                                        <h4 className="font-semibold mb-4 text-lg text-gray-900">К доплате:</h4>
                                        <div className="space-y-3">
                                            {/* Сначала отображаем текущего пользователя */}
                                            {currentUser && selectedFriends.includes(currentUser._id) && (
                                                <div className="flex justify-between p-3 rounded-xl bg-blue-50 border border-blue-100">
                                                    <span className="font-medium">{currentUser.name} (Вы):</span>
                                                    <span className="font-bold text-blue-700">{calculatePersonAmount(currentUser._id).toFixed(2)} ₽</span>
                                                </div>
                                            )}

                                            {/* Затем отображаем выбранных друзей */}
                                            {selectedFriends
                                                .filter(friendId => currentUser && friendId !== currentUser._id)
                                                .map(friendId => {
                                                    const friend = friends.find(f => f._id === friendId)
                                                    const amount = calculatePersonAmount(friendId)
                                                    if (!friend || amount === 0) return null

                                                    return (
                                                        <div key={friendId} className="flex justify-between p-3 rounded-xl bg-gray-50 border border-gray-100">
                                                            <span className="font-medium">{friend.name}:</span>
                                                            <span className="font-bold text-gray-700">{amount.toFixed(2)} ₽</span>
                                                        </div>
                                                    )
                                                })}
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Кнопки */}
                    <div className="flex flex-col sm:flex-row gap-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => router.back()}
                            className="flex-1 border-0 bg-gray-50 hover:bg-gray-100 text-gray-700 py-3 rounded-xl"
                        >
                            Отмена
                        </Button>
                        <Button
                            type="submit"
                            disabled={createBillMutation.isPending}
                            className="flex-1 bg-blue-600 hover:bg-blue-700 border-0 shadow-lg py-3 rounded-xl"
                        >
                            {createBillMutation.isPending ? "Создание..." : "Создать счет"}
                        </Button>
                    </div>
                </form>
            </div>
        </div>
    )
}
