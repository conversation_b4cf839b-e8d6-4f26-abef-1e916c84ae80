"use client";

import {useState} from "react";
import {enableStaticRendering, observer} from "mobx-react-lite";
import {MainLayout} from "@/components/layout/main-layout";
import {CreateBillModel, InputType} from "@/app/bills/new/model";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table";
import {toJS} from "mobx";
import {Input} from "@/components/ui/input";
import {navigateController} from "@/app/bills/new/lib";
import {Button} from "@/components/ui/button";
import {truncateNumber} from "@/lib/utils";
import {MinusCircle} from "lucide-react";

enableStaticRendering(typeof window === 'undefined')


const AddBillPage = observer(() => {
    const [createBillModel] = useState(() => new CreateBillModel())

    console.log(toJS(createBillModel.rows))
    console.log(createBillModel.toJSON())

    const renderByInputType = (inputType: keyof typeof InputType, val: string, rowIdx: number, colIdx: number) => {
        const totalSumCol = rowIdx !== 0 && colIdx === createBillModel.tableBody[0].length - 1
        const feeCol = rowIdx !== 0 &&colIdx === createBillModel.tableBody[0].length - 2

        switch (inputType) {
            case InputType.ITEM_TITLE:
                return (
                    <Input
                        id={`input-${rowIdx}-${colIdx}`}
                        placeholder={'Введите название'}
                        value={val}
                        onChange={(e) => {
                            createBillModel.setCell(rowIdx, colIdx, e.target.value);
                        }}
                        onKeyDown={navigateController}
                    />
                )
            case InputType.PRICE:
                return (
                    <Input
                        id={`input-${rowIdx}-${colIdx}`}
                        placeholder={'Введите цену'}
                        value={val}
                        onChange={(e) => {
                            createBillModel.setCell(rowIdx, colIdx, e.target.value);
                        }}
                        onKeyDown={navigateController}
                    />
                )
            case InputType.USER_SELECT:
                return (
                    <Input
                        id={`input-${rowIdx}-${colIdx}`}
                        placeholder={'Введите пользователя'}
                        value={val}
                        onChange={(e) => {
                            createBillModel.setCell(rowIdx, colIdx, e.target.value);
                        }}
                        onKeyDown={navigateController}
                    />
                )
            case InputType.TEXT:
                if(feeCol){
                    return truncateNumber(createBillModel.serviceFeeSum[rowIdx])
                }
                if(totalSumCol){
                    return truncateNumber(createBillModel.totalRowsSum[rowIdx])
                }
                return val
            default:
                return null

        }
    }

    return (
        <MainLayout>
            <div className='flex justify-between gap-2 mb-3'>
                <div className="flex gap-2">
                    <Button onClick={createBillModel.addRow}>
                        Добавить строку
                    </Button>
                    <Button onClick={createBillModel.addCol}>
                        Добавить колонку
                    </Button>
                    <Button variant="destructive" onClick={createBillModel.clearAll}>
                        Очистить все
                    </Button>
                    <Button variant="secondary" onClick={createBillModel.reset}>
                        Удалить все
                    </Button>
                </div>
                <Button onClick={() => console.log('DATA: ', createBillModel.toJSON())}>
                    Создать счет
                </Button>
            </div>
            <div className="rounded-md border">
                <Table border={1}>
                    <TableHeader>
                        <TableRow>
                            <TableHead></TableHead>
                            {
                                createBillModel.tableHeader.map((header, idx) => {

                                    return (
                                        <TableHead key={header.key}>
                                            {
                                                (idx === 0 || idx >= createBillModel.tableHeader.length - 2) || createBillModel.tableHeader.length < 5 ? null : <MinusCircle onClick={() => createBillModel.removeCol(idx)} />
                                            }
                                        </TableHead>
                                    )
                                })
                            }
                        </TableRow>
                        <TableRow>
                            <TableHead></TableHead>
                            {
                                createBillModel.tableHeader.map((header, idx) => (
                                    <TableHead key={header.key}>
                                        {
                                            renderByInputType(header.inputType, header.value, 0, idx)
                                        }
                                    </TableHead>
                                ))
                            }
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {createBillModel.tableBody.map((row, rowIdx) => (
                            <TableRow key={rowIdx}>
                                <TableCell width={30}>
                                    {
                                        createBillModel.tableBody.length < 2 ? null : <MinusCircle onClick={() => createBillModel.removeRow(rowIdx)} />
                                    }
                                </TableCell>
                                {
                                    row.map((col, colIdx) => (
                                        <TableCell key={col.key}>
                                            {renderByInputType(col.inputType, col.value, rowIdx + 1, colIdx)}
                                        </TableCell>
                                    ))
                                }
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        </MainLayout>
    )
})

AddBillPage.displayName = "AddBillPage"

export default AddBillPage


