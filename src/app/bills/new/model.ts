import {makeAutoObservable, toJS} from "mobx";
import {nanoid} from "nanoid";
import {calc} from "./lib"
import cloneDeep from 'lodash/cloneDeep';

export const InputType = {
    'ITEM_TITLE': 'ITEM_TITLE',
    'PRICE': 'PRICE',
    'USER_SELECT': 'USER_SELECT',
    'TEXT' : 'TEXT'
} as const

type ColumnsMapValue = {
    key: string;
    value: string;
    excludeServiceFee?: boolean;
    inputType: keyof typeof InputType;
}

const headerRow =  [
    {key: nanoid(), value: '№', excludeServiceFee: false, inputType: InputType.TEXT},
    {key: nanoid(), value: '', excludeServiceFee: false, inputType: InputType.ITEM_TITLE},
    {key: nanoid(), value: 'Сервисный сбор', excludeServiceFee: false, inputType: InputType.TEXT},
    {key: nanoid(), value: 'Итого', excludeServiceFee: false, inputType: InputType.TEXT},
]

const bodyRow = [
    {key: nanoid(), value: '', excludeServiceFee: false, inputType: InputType.USER_SELECT},
    {key: nanoid(), value: '', excludeServiceFee: false, inputType: InputType.PRICE},
    {key: nanoid(), value: '0', excludeServiceFee: false, inputType: InputType.TEXT},
    {key: nanoid(), value: '0', excludeServiceFee: false, inputType: InputType.TEXT},
]

const defaultRows = [headerRow, bodyRow]

class ServiceFeeModel {
    value: number

    constructor() {
        this.value = 0
        makeAutoObservable(this, {}, {autoBind: true})
        // void makePersistable(this,
        //     {
        //         name: 'ServiceFeeModel',
        //         properties: [
        //             {
        //                 key: 'value',
        //                 deserialize: (value: string) => Number(value),
        //                 serialize: (value: number) => value,
        //             }
        //         ],
        //     }
        // )
    }

    setValue (value: number) {
        this.value = Number(value)
    }
}

export class CreateBillModel {
    rows: ColumnsMapValue[][]

    serviceFee: ServiceFeeModel
    constructor() {
        console.log('created new model')
        this.rows = cloneDeep(defaultRows);
        this.serviceFee = new ServiceFeeModel()
        makeAutoObservable(this, {}, {autoBind: true})
    }

    get maxColSize (): number {
        let max = 0
        this.rows.forEach(cols => {
            max = Math.max(max, cols.length)
        })

        return max
    }

    get tableHeader () {
        return this.rows[0]
    }

    get tableBody () {
        return this.rows.slice(1)
    }

    addCol() {
        this.rows.forEach((row, idx) => {
            row.splice(row.length - 2, 0, {
                key: nanoid(),
                value: '',
                excludeServiceFee: this.serviceFee.value === 0,
                inputType: idx === 0 ? InputType.ITEM_TITLE : InputType.PRICE,
            })
        })
    }

    addRow() {
        const priceCols: ColumnsMapValue[] = []

        for (let i = 0; i < this.maxColSize - 3; i++){
            priceCols.push({
                key: nanoid(),
                value: '',
                excludeServiceFee: this.serviceFee.value === 0,
                inputType: InputType.PRICE,
            })
        }

        this.rows.push([
            {key: nanoid(), value: '', excludeServiceFee: false, inputType: InputType.USER_SELECT},
            ...priceCols,
            {key: nanoid(), value: '0', excludeServiceFee: false, inputType: InputType.TEXT},
            {key: nanoid(), value: '0', excludeServiceFee: false, inputType: InputType.TEXT},
        ])
    }

    generate (row: number, col: number) {
        this.reset()
        for (let i = 0; i < row; i++) {
            this.addRow()
        }
        for (let i = 0; i < col; i++) {
            this.addCol()
        }
    }

    removeRow (index: number) {
        this.rows.splice(index, 1)
    }

    removeCol (colIndex: number) {
        this.rows.forEach(row => {
            row.splice(colIndex, 1)
        })
    }

    setCellValue (rowIndex: number, colIndex: number, value: string) {
        this.rows[rowIndex][colIndex].value = value
    }

    setColumnServiceFee(colIndex: number, exclude: boolean) {
        this.rows.forEach(row => {
            if (row[colIndex]) {
                row[colIndex].excludeServiceFee = exclude;
            }
        });
    }

    get rowsSum () {
        const rows: number[] = []

        this.rows.forEach((row, idx) => {
            row.forEach((col) => {
                const value = calc(col.value)
                if(!isNaN(value)){
                    rows[idx] = (rows[idx] || 0) + value
                }
            })
        })

        return rows
    }

    get serviceFeeSum() {
        return this.rows.map((row) => {
            const sum = row.reduce((acc, col) => {
                if (!col.excludeServiceFee) {
                    const value = calc(col.value)
                    return acc + (isNaN(value) ? 0 : value)
                }
                return acc
            }, 0)
            return sum * (this.serviceFee.value / 100)
        })
    }

    get totalRowsSum() {
        return this.rows.map((row) => {
            const regularSum = row.reduce((acc, col) => {
                const value = calc(col.value)
                return acc + (isNaN(value) ? 0 : value)
            }, 0)

            const serviceFeeSum = row.reduce((acc, col) => {
                if (!col.excludeServiceFee) {
                    const value = calc(col.value)
                    return acc + (isNaN(value) ? 0 : value)
                }
                return acc
            }, 0) * (this.serviceFee.value / 100)

            return regularSum + serviceFeeSum
        })
    }

    get sum(): number {
        let sum = 0
        this.rows.forEach(row => {
            let regularSum = 0
            let serviceFeeSum = 0

            row.forEach(col => {
                const value = calc(col.value)
                if(!isNaN(value)){
                    regularSum += value
                    if (!col.excludeServiceFee) {
                        serviceFeeSum += value
                    }
                }
            })

            sum += regularSum + (serviceFeeSum * (this.serviceFee.value / 100))
        })

        return sum
    }

    setCell (rowIndex: number, colIndex: number, value: string) {
        this.rows[rowIndex][colIndex].value = value
    }

    clearAll () {
        this.rows.forEach((row) => {
            row.forEach((col, colIdx) => {
                if(colIdx === 0){
                    return
                }
                col.value = ''
            })
        })
    }

    toJSON () {
        const data = this.rows
        const headerRow = data[0].filter(col => col.value !== '№');
        const titles = headerRow.map(col => col.value);

        // Обрабатываем остальные строки
        return data.slice(1).map(row => {
            const filtered = row.filter(col => col.value !== '№');

            const user = filtered[0].value;
            const items = [];
            let totalSum = 0

            for (let i = 1; i < filtered.length - 2; i++) {
                if (Number.isNaN(calc(filtered[i].value))) {
                    continue;
                }
                const title = titles[i - 1]; // смещение, т.к. 0-й элемент — имя
                const price = parseFloat(filtered[i].value || '0');
                if(price === 0){
                    continue
                }
                let priceWithVat = price
                const vat = filtered[i].excludeServiceFee;

                if(vat){
                    priceWithVat = price + (price * (this.serviceFee.value / 100))
                }

                totalSum += priceWithVat

                items.push({ title, price, vat, priceWithVat });
            }

            return { user, items, totalSum };
        });
    }

    reset () {
        this.rows = cloneDeep(defaultRows)
        this.serviceFee.setValue(0)
    }

}