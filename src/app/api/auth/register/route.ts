import { NextRequest, NextResponse } from "next/server"
import bcrypt from "bcryptjs"
import dbConnect from "@/lib/mongodb"
import { User } from "@/models/User"
import { registerApiSchema } from "@/lib/schemas"
import { z } from "zod";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, email, password } = registerApiSchema.parse(body)

    await dbConnect()

    // Проверяем, существует ли пользователь
    const existingUser = await User.findOne({ email })
    if (existingUser) {
      return NextResponse.json(
        { error: "Пользователь с таким email уже существует" },
        { status: 400 }
      )
    }

    // Хешируем пароль
    const passwordHash = await bcrypt.hash(password, 12)

    // Создаем пользователя
    const user = await User.create({
      name,
      email,
      passwordHash,
    })

    return NextResponse.json(
      { message: "Пользователь успешно создан", userId: user._id },
      { status: 201 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    console.error("Registration error:", error)
    return NextResponse.json(
      { error: "Внутренняя ошибка сервера" },
      { status: 500 }
    )
  }
}
