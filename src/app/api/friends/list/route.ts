import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import dbConnect from "@/lib/mongodb"
import { User } from "@/models/User"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Не авторизован" },
        { status: 401 }
      )
    }

    await dbConnect()

    const currentUser = await User.findById(session.user.id)
    
    if (!currentUser) {
      return NextResponse.json(
        { error: "Пользователь не найден" },
        { status: 404 }
      )
    }

    // Получаем список всех друзей (первые 20)
    const friends = await User.find({
      _id: { $in: currentUser.following }
    })
    .select("name email")
    .limit(20)

    return NextResponse.json({ friends })
  } catch (error) {
    console.error("Error fetching friends list:", error)
    return NextResponse.json(
      { error: "Внутренняя ошибка сервера" },
      { status: 500 }
    )
  }
}
