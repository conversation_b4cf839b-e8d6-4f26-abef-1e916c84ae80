import {Navbar} from "@/components/layout/navbar";
import {FC, ReactNode} from "react";

export const MainLayout: FC<{children?: ReactNode}> = ({children}) => {
    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
            <Navbar />
            <div className="container mx-auto px-4 py-8">
                {children}
            </div>
        </div>
    )
}