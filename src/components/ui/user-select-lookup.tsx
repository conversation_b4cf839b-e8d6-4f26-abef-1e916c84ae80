"use client"

import { useState, useEffect } from "react"
import { Check, ChevronsUpDown, User as UserIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useSearchFriends, useFriendsList } from "@/hooks/use-api"
import { User } from "@/lib/types"

interface UserSelectLookupProps {
  value: string
  onChange: (value: string, user?: User) => void
  placeholder?: string
  className?: string
  id?: string
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void
}

export function UserSelectLookup({
  value,
  onChange,
  placeholder = "Выберите друга...",
  className,
  id,
  onKeyDown
}: UserSelectLookupProps) {
  const [open, setOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedUser, setSelectedUser] = useState<User | null>(null)

  // Получаем список всех друзей (20 по умолчанию)
  const { data: friendsList = [], isLoading: isLoadingList } = useFriendsList()

  // Поиск друзей при вводе
  const { data: searchResults = [], isLoading: isSearching } = useSearchFriends(
    searchQuery,
    searchQuery.length > 0
  )

  // Комбинируем результаты: если есть поиск, показываем результаты поиска, иначе показываем список друзей
  const friends = searchQuery.length > 0 ? searchResults : friendsList
  const isLoading = searchQuery.length > 0 ? isSearching : isLoadingList

  // Находим выбранного пользователя по значению
  useEffect(() => {
    if (value) {
      const user = friendsList.find(friend => friend.name === value || friend._id === value)
      setSelectedUser(user || null)
    } else {
      setSelectedUser(null)
    }
  }, [value, friendsList])

  const updatePosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect()
      const dropdownHeight = 240 // max-h-60 = 240px
      const viewportHeight = window.innerHeight
      const spaceBelow = viewportHeight - rect.bottom
      const spaceAbove = rect.top

      // Определяем, показывать ли dropdown сверху или снизу
      const showAbove = spaceBelow < dropdownHeight && spaceAbove > spaceBelow

      setPosition({
        top: showAbove
          ? rect.top + window.scrollY - dropdownHeight
          : rect.bottom + window.scrollY,
        left: Math.max(8, Math.min(rect.left + window.scrollX, window.innerWidth - rect.width - 8)),
        width: rect.width
      })
    }
  }

  const handleSelectUser = (user: User) => {
    setSelectedUser(user)
    onChange(user._id, user)
    setOpen(false)
    setSearchQuery("")
  }

  const handleToggleOpen = () => {
    if (!open) {
      updatePosition()
    }
    setOpen(!open)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape" && open) {
      e.preventDefault()
      setOpen(false)
      return
    }

    if (onKeyDown) {
      onKeyDown(e as React.KeyboardEvent<HTMLInputElement>)
    }
  }

  return (
    <div className="relative" ref={containerRef}>
      <Button
        ref={buttonRef}
        variant="outline"
        role="combobox"
        aria-expanded={open}
        className={cn(
          "w-full justify-between h-10",
          !selectedUser && "text-muted-foreground",
          className
        )}
        id={id}
        onKeyDown={handleKeyDown}
        onClick={handleToggleOpen}
      >
        {selectedUser ? (
          <div className="flex items-center space-x-2">
            <UserIcon className="h-4 w-4" />
            <span className="truncate">{selectedUser.name}</span>
          </div>
        ) : (
          placeholder
        )}
        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
      </Button>

      {open && (
        <Portal>
          <div
            data-portal-dropdown
            className="fixed z-50 rounded-md border bg-white p-0 shadow-md max-h-60 overflow-hidden"
            style={{
              top: position.top,
              left: position.left,
              width: position.width,
            }}
          >
            <Command>
              <CommandInput
                placeholder="Поиск друзей..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <CommandList>
                {friends.length === 0 ? (
                  <CommandEmpty>
                    {isLoading ? "Загрузка..." : "Друзья не найдены"}
                  </CommandEmpty>
                ) : (
                  <CommandGroup>
                    {friends.map((friend) => (
                      <CommandItem
                        key={friend._id}
                        value={friend.name}
                        onSelect={() => handleSelectUser(friend)}
                        className="flex items-center space-x-3"
                      >
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            selectedUser?._id === friend._id ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <UserIcon className="h-4 w-4 text-gray-400" />
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-sm truncate">
                            {friend.name}
                          </div>
                          <div className="text-xs text-gray-500 truncate">
                            {friend.email}
                          </div>
                        </div>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                )}
              </CommandList>
            </Command>
          </div>
        </Portal>
      )}
    </div>
  )
}
