"use client"

import { useState, useRef, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { useSearchFriends } from "@/hooks/use-api"
import { User } from "@/lib/types"
import { cn } from "@/lib/utils"
import { Check, ChevronDown, Search, User as UserIcon } from "lucide-react"

interface UserSelectLookupProps {
  value: string
  onChange: (value: string, user?: User) => void
  placeholder?: string
  className?: string
  id?: string
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void
}

export function UserSelectLookup({
  value,
  onChange,
  placeholder = "Введите имя пользователя...",
  className,
  id,
  onKeyDown
}: UserSelectLookupProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState(value)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  
  const inputRef = useRef<HTMLInputElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // React Query hook для поиска друзей
  const { data: friends = [], isLoading } = useSearchFriends(
    searchQuery,
    searchQuery.length > 0 && isOpen
  )

  // Обновляем searchQuery когда value изменяется извне
  useEffect(() => {
    if (value !== searchQuery) {
      setSearchQuery(value)
    }
  }, [value])

  // Закрываем dropdown при клике вне компонента
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
        setSelectedIndex(-1)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setSearchQuery(newValue)
    onChange(newValue)
    setIsOpen(true)
    setSelectedIndex(-1)
    setSelectedUser(null)
  }

  const handleInputFocus = () => {
    setIsOpen(true)
  }

  const handleSelectUser = (user: User) => {
    setSearchQuery(user.name)
    setSelectedUser(user)
    onChange(user.name, user)
    setIsOpen(false)
    setSelectedIndex(-1)
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (onKeyDown) {
      onKeyDown(e)
    }

    if (!isOpen) {
      if (e.key === "ArrowDown" || e.key === "Enter") {
        e.preventDefault()
        setIsOpen(true)
        return
      }
      return
    }

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < friends.length - 1 ? prev + 1 : 0
        )
        break
      case "ArrowUp":
        e.preventDefault()
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : friends.length - 1
        )
        break
      case "Enter":
        e.preventDefault()
        if (selectedIndex >= 0 && friends[selectedIndex]) {
          handleSelectUser(friends[selectedIndex])
        }
        break
      case "Escape":
        e.preventDefault()
        setIsOpen(false)
        setSelectedIndex(-1)
        break
    }
  }

  return (
    <div className="relative">
      <div className="relative">
        <Input
          ref={inputRef}
          id={id}
          value={searchQuery}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={cn(
            "pr-10",
            className
          )}
          autoComplete="off"
        />
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          {isLoading ? (
            <div className="h-4 w-4">
              <LoadingSpinner />
            </div>
          ) : (
            <Search className="h-4 w-4 text-gray-400" />
          )}
        </div>
      </div>

      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto"
        >
          {isLoading ? (
            <div className="p-4 text-center">
              <LoadingSpinner message="Поиск друзей..." />
            </div>
          ) : friends.length > 0 ? (
            <div className="py-1">
              {friends.map((friend, index) => (
                <div
                  key={friend._id}
                  className={cn(
                    "px-4 py-2 cursor-pointer flex items-center space-x-3 hover:bg-gray-100",
                    selectedIndex === index && "bg-blue-50 text-blue-700"
                  )}
                  onClick={() => handleSelectUser(friend)}
                  onMouseEnter={() => setSelectedIndex(index)}
                >
                  <UserIcon className="h-4 w-4 text-gray-400" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm truncate">
                      {friend.name}
                    </div>
                    <div className="text-xs text-gray-500 truncate">
                      {friend.email}
                    </div>
                  </div>
                  {selectedUser?._id === friend._id && (
                    <Check className="h-4 w-4 text-green-600" />
                  )}
                </div>
              ))}
            </div>
          ) : searchQuery.length > 0 ? (
            <div className="p-4 text-center text-gray-500 text-sm">
              Друзья не найдены
            </div>
          ) : (
            <div className="p-4 text-center text-gray-500 text-sm">
              Начните вводить имя друга
            </div>
          )}
        </div>
      )}
    </div>
  )
}
