"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

// Простая реализация Popover с порталом
interface PopoverContextType {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const PopoverContext = React.createContext<PopoverContextType | null>(null)

interface PopoverProps {
  children: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

const Popover = ({ children, open = false, onOpenChange }: PopoverProps) => {
  return (
    <PopoverContext.Provider value={{ open, onOpenChange: onOpenChange || (() => {}) }}>
      {children}
    </PopoverContext.Provider>
  )
}

interface PopoverTriggerProps {
  children: React.ReactNode
  asChild?: boolean
}

const PopoverTrigger = ({ children, asChild }: PopoverTriggerProps) => {
  const context = React.useContext(PopoverContext)

  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children, {
      onClick: (e: React.MouseEvent) => {
        children.props.onClick?.(e)
        context?.onOpenChange(!context.open)
      }
    })
  }

  return (
    <div onClick={() => context?.onOpenChange(!context.open)}>
      {children}
    </div>
  )
}

interface PopoverContentProps extends React.HTMLAttributes<HTMLDivElement> {
  align?: "start" | "center" | "end"
  sideOffset?: number
}

const PopoverContent = React.forwardRef<HTMLDivElement, PopoverContentProps>(
  ({ className, align = "start", sideOffset = 4, children, ...props }, ref) => {
    const context = React.useContext(PopoverContext)
    const [mounted, setMounted] = React.useState(false)
    const [position, setPosition] = React.useState({ top: 0, left: 0, width: 200 })

    React.useEffect(() => {
      setMounted(true)
    }, [])

    React.useEffect(() => {
      if (!context?.open || !mounted) return

      // Находим trigger элемент
      const trigger = document.querySelector('[role="combobox"]') as HTMLElement
      if (!trigger) return

      const updatePosition = () => {
        const rect = trigger.getBoundingClientRect()
        const dropdownHeight = 240
        const viewportHeight = window.innerHeight
        const spaceBelow = viewportHeight - rect.bottom
        const spaceAbove = rect.top

        const showAbove = spaceBelow < dropdownHeight && spaceAbove > spaceBelow

        setPosition({
          top: showAbove
            ? rect.top + window.scrollY - dropdownHeight - sideOffset
            : rect.bottom + window.scrollY + sideOffset,
          left: Math.max(8, Math.min(rect.left + window.scrollX, window.innerWidth - rect.width - 8)),
          width: rect.width
        })
      }

      updatePosition()

      const handleScroll = () => updatePosition()
      const handleResize = () => updatePosition()
      const handleClickOutside = (event: MouseEvent) => {
        const target = event.target as Node
        if (!trigger.contains(target) && !document.querySelector('[data-popover-content]')?.contains(target)) {
          context?.onOpenChange(false)
        }
      }

      window.addEventListener("scroll", handleScroll, true)
      window.addEventListener("resize", handleResize)
      document.addEventListener("mousedown", handleClickOutside)

      return () => {
        window.removeEventListener("scroll", handleScroll, true)
        window.removeEventListener("resize", handleResize)
        document.removeEventListener("mousedown", handleClickOutside)
      }
    }, [context?.open, mounted, sideOffset])

    if (!context?.open || !mounted) {
      return null
    }

    return (
      <>
        {typeof window !== 'undefined' &&
          React.createPortal(
            <div
              ref={ref}
              data-popover-content
              className={cn(
                "fixed z-50 rounded-md border bg-popover text-popover-foreground shadow-md outline-none max-h-60 overflow-hidden",
                className
              )}
              style={{
                top: position.top,
                left: position.left,
                width: position.width,
              }}
              {...props}
            >
              {children}
            </div>,
            document.body
          )
        }
      </>
    )
  }
)
PopoverContent.displayName = "PopoverContent"

export { Popover, PopoverTrigger, PopoverContent }
