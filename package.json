{"name": "next-mongoose", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.3.0", "@next-auth/mongodb-adapter": "^1.1.0", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.80.6", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^4.1.0", "lodash": "^4.17.21", "lucide-react": "^0.294.0", "mobx": "^6.13.7", "mobx-persist-store": "^1.1.8", "mobx-react-lite": "^4.1.0", "mongodb": "^6.16.0", "mongoose": "^8.0.0", "nanoid": "^5.1.5", "next": "15.3.3", "next-auth": "^4.24.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.48.0", "recharts": "^2.15.3", "tailwind-merge": "^2.0.0", "zod": "^3.22.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tanstack/react-query-devtools": "^5.80.6", "@types/bcryptjs": "^2.4.6", "@types/lodash": "^4.17.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}